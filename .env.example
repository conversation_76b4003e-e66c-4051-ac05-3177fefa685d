# OpenAutomate Public Site Environment Variables
# Copy this file to .env.local and update the values

# Production site URL (PRIMARY DOMAIN - with www)
# IMPORTANT: Use www.openautomate.io consistently as your primary domain
NEXT_PUBLIC_SITE_URL=https://www.openautomate.io

# Orchestrator/Cloud platform URL
NEXT_PUBLIC_ORCHESTRATOR_URL=https://cloud.openautomate.io

# API base URL (if different from orchestrator)
NEXT_PUBLIC_API_URL=https://api.openautomate.io

# Google Analytics (optional)
# NEXT_PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# Google Search Console (optional - for verification)
# NEXT_PUBLIC_GOOGLE_SITE_VERIFICATION=your-verification-code

# Social Media (optional - update in config.ts)
# NEXT_PUBLIC_TWITTER_HANDLE=@openautomate
# NEXT_PUBLIC_LINKEDIN_URL=https://linkedin.com/company/openautomate
# NEXT_PUBLIC_GITHUB_URL=https://github.com/openautomate
