import {
  ArrowRight,
  Check,
  ChevronLeft,
  ChevronRight,
  Command,
  CreditCard,
  File,
  FileText,
  Github,
  HelpCircle,
  Image,
  Laptop,
  Loader2,
  Moon,
  MoreVertical,
  Pizza,
  Plus,
  Settings,
  SunMedium,
  Trash,
  Twitter,
  User,
  X,
  type LucideIcon,
  Play,
  Pause,
  Home,
  BarChart,
  Cog,
  LogOut,
  RefreshCw,
  Newspaper,
  NotebookPen,
  Mail,
} from 'lucide-react'

export type Icon = LucideIcon

export const Icons = {
  logo: Command,
  close: X,
  Spinner: Loader2,
  chevronLeft: ChevronLeft,
  chevronRight: ChevronRight,
  trash: Trash,
  settings: Settings,
  billing: CreditCard,
  ellipsis: MoreVertical,
  add: Plus,
  warning: HelpCircle,
  user: User,
  arrowRight: ArrowRight,
  help: HelpCircle,
  pizza: Pizza,
  sun: SunMedium,
  moon: Moon,
  laptop: Laptop,
  gitHub: Github,
  twitter: Twitter,
  check: Check,
  file: File,
  fileText: FileText,
  image: Image,
  play: Play,
  pause: Pause,
  home: Home,
  chart: <PERSON><PERSON><PERSON>,
  cog: Cog,
  logout: LogOut,
  refresh: RefreshCw,
  about: Newspaper,
  guide: NotebookPen,
  contact: Mail,
}
