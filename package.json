{"name": "openautomate-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "set NODE_OPTIONS=--max_old_space_size=4096 && next dev -p 3000", "build": "set NODE_OPTIONS=--max_old_space_size=4096 && next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix --ext .js,.jsx,.ts,.tsx", "format": "prettier --write .", "format:check": "prettier --check .", "sonar": "sonar-scanner", "test": "jest --coverage"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-collapsible": "^1.1.7", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toggle": "^1.1.6", "@radix-ui/react-toggle-group": "^1.1.7", "@radix-ui/react-tooltip": "^1.2.3", "@tanstack/react-table": "^8.21.3", "animejs": "^4.0.2", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^3.6.0", "framer-motion": "^12.10.0", "lucide-react": "^0.487.0", "next": "15.2.4", "next-themes": "^0.4.6", "react": "^19.0.0", "react-day-picker": "^9.6.7", "react-dom": "^19.0.0", "react-hook-form": "^7.55.0", "recharts": "^2.15.3", "tailwind-merge": "^3.2.0", "tw-animate-css": "^1.2.5", "zod": "^3.24.2"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/animejs": "^3.1.13", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.2.4", "eslint-config-prettier": "^10.1.2", "eslint-plugin-prettier": "^5.2.6", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}