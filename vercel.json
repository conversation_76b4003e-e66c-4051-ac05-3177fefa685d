{"version": 2, "public": true, "github": {"silent": true}, "redirects": [{"source": "/(.*)", "has": [{"type": "host", "value": "www.openautomate.io"}], "destination": "https://openautomate.io/$1", "permanent": true}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-DNS-Prefetch-Control", "value": "on"}, {"key": "Strict-Transport-Security", "value": "max-age=63072000; includeSubDomains; preload"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}, {"key": "Permissions-Policy", "value": "camera=(), microphone=(), geolocation=()"}]}, {"source": "/sitemap.xml", "headers": [{"key": "Content-Type", "value": "application/xml"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/robots.txt", "headers": [{"key": "Content-Type", "value": "text/plain"}, {"key": "Cache-Control", "value": "public, max-age=86400"}]}, {"source": "/(.*\\.(ico|png|jpg|jpeg|gif|svg|webp|avif))", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}]}